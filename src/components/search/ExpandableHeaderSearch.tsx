import React, { useState, useEffect, useRef } from "react";
import { <PERSON>, Sparkles, Zap, HelpCircle, Users } from "lucide-react";
import UnifiedSearchSelect from "./UnifiedSearchSelect";
import SearchSuggestions from "./SearchSuggestions";
import DateRangePicker from "./DateRangePicker";
import GuestSelector from "./GuestSelector";
import { formatDateForAPI } from "../../utils/dateUtils";
import "../../styles/expandable-header-search.css";
import "../../styles/airbnb-header-search.css";
import "../../styles/search-suggestions.css";
import { useIsMobile } from "../../hooks/use-mobile";

interface ExpandableHeaderSearchProps {
  isVisible?: boolean;
  isExpandedView?: boolean;
}

const ExpandableHeaderSearch: React.FC<ExpandableHeaderSearchProps> = ({
  isExpandedView,
}) => {
  const [isExpanded, setIsExpanded] = useState(isExpandedView || false);
  const [isAiSearch, setIsAiSearch] = useState(true); // Default to AI search
  const [destination, setDestination] = useState("");
  const [searchType, setSearchType] = useState<"destination" | "hotel">(
    "destination"
  );
  const [conversation, setConversation] = useState("");
  const [isFocused, setIsFocused] = useState(false);
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const headerWrapperRef = useRef<HTMLDivElement | null>(null);
  const [currentPath, setCurrentPath] = useState<string>("");
  const [isSearchPage, setIsSearchPage] = useState(false);
  const isMobile = useIsMobile();

  // Date picker state
  const [checkInDate, setCheckInDate] = useState<Date | null>(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  });
  const [checkOutDate, setCheckOutDate] = useState<Date | null>(() => {
    const checkoutDate = new Date();
    checkoutDate.setDate(checkoutDate.getDate() + 3); // 2 nights stay by default
    return checkoutDate;
  });
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Guest selector state
  const [guests, setGuests] = useState({ adults: 1, children: 0, infants: 0 });
  const [isGuestSelectorOpen, setIsGuestSelectorOpen] = useState(false);
  const [isPetsAllowed, setIsPetsAllowed] = useState(false);

  // State to track if component is in header or hero section
  const [isInHeader, setIsInHeader] = useState(false);
  const [isInHero, setIsInHero] = useState(false);

  // Function to open the AI help modal
  const openAIHelpModal = () => {
    if (typeof window !== "undefined") {
      // Ensure the function exists before calling it
      if ((window as any).openAIHelpModal) {
        (window as any).openAIHelpModal();
      } else {
        console.warn("AI Help Modal function not available");
      }
    }
  };

  // Find the header wrapper element and set current path
  useEffect(() => {
    headerWrapperRef.current = document.querySelector(".header-wrapper");

    // Get current path
    if (typeof window !== "undefined") {
      const path = window.location.pathname;
      setCurrentPath(path);

      // Check if we're on the search page
      const isOnSearchPage = path === "/search";
      setIsSearchPage(isOnSearchPage);

      // Check if this component is inside the header
      if (searchContainerRef.current) {
        const isInHeaderSection =
          searchContainerRef.current.closest(".header-wrapper") !== null;
        setIsInHeader(isInHeaderSection);
      }

      // Set search mode based on current page
      if (path === "/ai-search") {
        setIsAiSearch(true);
      } else if (isOnSearchPage) {
        // Get search parameters from URL
        const searchParams = new URLSearchParams(window.location.search);

        // Only set to false if explicitly specified in URL
        const aiSearchParam = searchParams.get("ai_search");
        if (aiSearchParam === "false") {
          setIsAiSearch(false);
        }

        // Set destination from URL if available
        const destinationId = searchParams.get("destination_id");
        if (destinationId) {
          setDestination(destinationId);
        }

        // Set dates from URL if available
        const checkIn = searchParams.get("check_in");
        const checkOut = searchParams.get("check_out");
        if (checkIn) {
          setCheckInDate(new Date(checkIn));
        }
        if (checkOut) {
          setCheckOutDate(new Date(checkOut));
        }

        // Set guests from URL if available
        const adultsParam = searchParams.get("adults");
        const childrenParam = searchParams.get("children");
        const infantsParam = searchParams.get("infants");
        const petsParam = searchParams.get("is_pets_allowed");

        if (adultsParam || childrenParam || infantsParam) {
          setGuests({
            adults: adultsParam ? parseInt(adultsParam) : 1,
            children: childrenParam ? parseInt(childrenParam) : 0,
            infants: infantsParam ? parseInt(infantsParam) : 0,
          });
        }

        if (petsParam) {
          setIsPetsAllowed(petsParam === "true");
        }

        // Auto-expand the search on search page
        setIsExpanded(true);

        // Add expanded class to header for search page
        if (headerWrapperRef.current) {
          headerWrapperRef.current.classList.add("header-expanded");

          // Also add expanded-header class to primary header for additional styling
          const primaryHeader = document.querySelector(".primary-header");
          if (primaryHeader) {
            primaryHeader.classList.add("expanded-header");
          }
        }
      } else {
        // Check URL parameters for ai_search
        const searchParams = new URLSearchParams(window.location.search);
        const aiSearchParam = searchParams.get("ai_search");
        // Only set to false if explicitly specified
        if (aiSearchParam === "false") {
          setIsAiSearch(false);
        }
      }
    }
  }, []);

  // Check if component is in header or hero section
  useEffect(() => {
    // Function to check if component is in header or hero section
    const checkComponentLocation = () => {
      if (searchContainerRef.current) {
        const isInHeaderSection =
          searchContainerRef.current.closest(".header-wrapper") !== null;
        setIsInHeader(isInHeaderSection);

        const isInHeroSection =
          searchContainerRef.current.closest(".hero-content") !== null ||
          searchContainerRef.current.closest(".hero-section") !== null;
        setIsInHero(isInHeroSection);
      }
    };

    // Check on mount
    checkComponentLocation();

    // Create a MutationObserver to detect DOM changes
    const observer = new MutationObserver(checkComponentLocation);

    // Start observing the document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup
    return () => {
      observer.disconnect();
    };
  }, []);

  // Handle click outside to collapse the search
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node) &&
        isExpanded &&
        !isExpandedView // Only collapse if isExpandedView is not true
      ) {
        resetSearchState();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded, isExpandedView]);

  // Function to reset search state
  const resetSearchState = () => {
    // Only set isExpanded to false if isExpandedView is not true and not on search page
    if (!isExpandedView && !isSearchPage) {
      setIsExpanded(false);

      // Reset form values only if not on search page
      if (!isSearchPage) {
        setDestination("");
        setConversation("");
      }

      // Remove expanded class from header
      if (headerWrapperRef.current) {
        headerWrapperRef.current.classList.remove("header-expanded");

        // Also remove expanded-header class from primary header
        const primaryHeader = document.querySelector(".primary-header");
        if (primaryHeader) {
          primaryHeader.classList.remove("expanded-header");
        }
      }
    }
  };

  // Listen for scroll events to reset search state
  useEffect(() => {
    const handleScroll = () => {
      // Only collapse on scroll if isExpandedView is not true, not on search page, and not on mobile
      if (isExpanded && !isExpandedView && !isSearchPage && !isMobile) {
        resetSearchState();
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isExpanded, isExpandedView, isSearchPage, isMobile]);

  // Function to expand the search
  const expandSearch = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsExpanded(true);

    // Add expanded class to header
    if (headerWrapperRef.current) {
      headerWrapperRef.current.classList.add("header-expanded");

      // Also add expanded-header class to primary header for additional styling
      const primaryHeader = document.querySelector(".primary-header");
      if (primaryHeader) {
        primaryHeader.classList.add("expanded-header");
      }
    }
  };

  // Function to handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Build the search URL
    const params = new URLSearchParams();

    if (isAiSearch) {
      if (conversation.trim()) {
        params.set("query", conversation);
        params.set("user_message", conversation);
        params.set("ai_search", "true");
        window.location.href = `/ai-search?${params.toString()}`;
      }
    } else {
      if (destination) {
        if (searchType === "destination") {
          params.set("destination_id", destination);
        } else if (searchType === "hotel") {
          params.set("hotel_id", destination);
        }
      }

      // Add date parameters if available
      if (checkInDate) {
        params.set("check_in", formatDateForAPI(checkInDate));
      }

      if (checkOutDate) {
        params.set("check_out", formatDateForAPI(checkOutDate));
      }

      // Add guest parameters
      params.set("adults", guests.adults.toString());
      params.set("children", guests.children.toString());
      params.set("infants", guests.infants.toString());

      // Add pets parameter if enabled
      if (isPetsAllowed) {
        params.set("is_pets_allowed", "true");
      }

      params.set("ai_search", "false");

      // If searching for a specific hotel, redirect to hotel details page
      if (searchType === "hotel" && destination) {
        window.location.href = `/stays/${destination}?${params.toString()}`;
      } else {
        window.location.href = `/search?${params.toString()}`;
      }
    }
  };

  // Handle date range changes
  const handleDateRangeChange = (
    startDate: Date | null,
    endDate: Date | null,
    _submitForm?: boolean // Unused parameter, but kept for compatibility
  ) => {
    setCheckInDate(startDate);
    setCheckOutDate(endDate);

    // Only close the date picker if both dates are selected
    if (startDate && endDate) {
      setIsDatePickerOpen(false);
      // Don't submit the form automatically - wait for search button click
    }
  };

  // Handle guest changes - automatically update the state
  const handleGuestChange = (
    newGuests: {
      adults: number;
      children: number;
      infants: number;
    },
    _submitForm?: boolean, // Unused parameter, but kept for compatibility
    newIsPetsAllowed?: boolean
  ) => {
    setGuests(newGuests);
    if (newIsPetsAllowed !== undefined) {
      setIsPetsAllowed(newIsPetsAllowed);
    }
    // Don't close the selector automatically - let the user close it by clicking elsewhere
    // Don't submit the form - wait for search button click
  };

  // Handle unified search selection
  const handleUnifiedSearchChange = (
    value: string,
    type: "destination" | "hotel"
  ) => {
    setDestination(value);
    setSearchType(type);
  };

  // Helper function to dispatch search mode change event
  const dispatchSearchModeEvent = (isAiMode: boolean) => {
    // Dispatch event for other components that might need to know
    if (typeof document !== "undefined") {
      const event = new CustomEvent("searchModeChanged", {
        detail: { isAiSearch: isAiMode },
      });
      document.dispatchEvent(event);
    }
  };

  return (
    <div
      className="expandable-search-wrapper visible"
      data-expandable-search
      ref={searchContainerRef}
    >
      {!isExpanded ? (
        // Collapsed state - simple search bar
        <div className="collapsed-search-container" onClick={expandSearch}>
          <div className="collapsed-search-inner">
            {isMobile ? (
              // Simplified view for mobile
              <div className="search-tab">Search destinations</div>
            ) : (
              // Full view for desktop
              <>
                <div className="search-tab">Anywhere</div>
                <div className="search-divider"></div>
                <div className="search-tab">Anytime</div>
                <div className="search-divider"></div>
                <div className="search-tab search-tab-last">Anyone</div>
              </>
            )}
            <button
              type="button"
              className="search-button-small"
              onClick={(e) => {
                e.stopPropagation();
                expandSearch(e);
              }}
            >
              <Search size={18} className="search-icon" />
            </button>
          </div>
        </div>
      ) : (
        // Expanded state - full search interface
        <div className="search-wrapper expanded-search-wrapper">
          <div className="search-toggle-container">
            <div className="search-toggle-wrapper">
              <button
                type="button"
                className={`toggle-option ai-option ${
                  isAiSearch ? "active" : ""
                }`}
                onClick={() => {
                  // On home page, don't navigate, just toggle the mode
                  if (currentPath === "/") {
                    setIsAiSearch(true);
                    dispatchSearchModeEvent(true);
                  }
                  // On AI search page, just toggle the mode
                  else if (currentPath === "/ai-search") {
                    setIsAiSearch(true);
                    dispatchSearchModeEvent(true);
                  }
                  // On other pages, navigate to ai-search
                  else {
                    window.location.href = "/ai-search";
                  }
                }}
              >
                <Sparkles size={14} className="sparkle-icon" />
                Perfect Piste AI
              </button>
              <button
                type="button"
                className={`toggle-option ${!isAiSearch ? "active" : ""}`}
                onClick={() => {
                  // If on ai-search page, navigate to search page
                  if (currentPath === "/ai-search") {
                    window.location.href = "/search";
                  } else {
                    // Otherwise just toggle the mode
                    setIsAiSearch(false);
                    dispatchSearchModeEvent(false);
                  }
                }}
              >
                Regular Search
              </button>
              <div
                className={`toggle-slider ${isAiSearch ? "ai-active" : ""}`}
              ></div>
            </div>
          </div>

          {isAiSearch ? (
            // AI Search Mode - Show message on AI Search page, input field on other pages
            currentPath === "/ai-search" ? (
              // Message for AI Search page
              <div className="airbnb-header-search ai-mode-prompt">
                <div className="ai-prompt-container">
                  <Zap size={20} className="ai-icon" />
                  <div className="ai-prompt-text">
                    <span className="ai-prompt-title">AI Concierge Active</span>
                    <span className="ai-prompt-subtitle">
                      Use the chat below to ask about luxury ski accommodations
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              // Input field for other pages
              <div>
                <div>
                  <form
                    className="airbnb-header-search"
                    onSubmit={handleSearch}
                  >
                    <div className="search-section ai-search-input-section">
                      <input
                        type="text"
                        value={conversation}
                        onChange={(e) => setConversation(e.target.value)}
                        onFocus={() => setIsFocused(true)}
                        onBlur={() => setIsFocused(false)}
                        placeholder={
                          isFocused && !conversation
                            ? "e.g., I'm planning a romantic weekend getaway at a luxury ski chalet with spa access."
                            : isMobile
                            ? "Ask about ski accommodations..."
                            : "I am looking for..."
                        }
                        className="ai-search-input"
                      />
                    </div>
                    <div className="search-buttons-container">
                      <button
                        type="button"
                        className="info-button"
                        title="Learn how to use AI search"
                        aria-label="Learn how to use AI search"
                        onClick={(e) => {
                          e.preventDefault();
                          openAIHelpModal();
                        }}
                      >
                        <HelpCircle size={16} color="#3566ab" />
                        <span className="info-text">Help to use AI</span>
                      </button>
                      <button
                        type="submit"
                        className="flex items-center justify-center bg-[#3566ab] text-white h-[42px] w-[42px] min-w-[42px] rounded-full border-none mr-2 cursor-pointer transition-all duration-200 shadow-md hover:scale-105 hover:bg-[#2a5089] hover:shadow-lg active:scale-[0.98]"
                        disabled={!conversation.trim()}
                      >
                        <Search size={20} color="white" />
                      </button>
                    </div>
                  </form>
                </div>

                {/* WhatsApp Meta AI style suggestions - only show on home page and not in header */}
                {currentPath === "/" && !isInHeader && (
                  <div className="suggestions-wrapper hero-only-suggestions">
                    <SearchSuggestions
                      onSelectSuggestion={(suggestion) => {
                        setConversation(suggestion);
                      }}
                    />
                  </div>
                )}
              </div>
            )
          ) : (
            // Regular Search Form
            <form
              className="flex items-center bg-white rounded-[28px] border border-[rgba(53,102,171,0.15)] shadow-sm h-14 w-full max-w-[848px] transition-all duration-300 hover:shadow-md hover:border-[rgba(53,102,171,0.3)]"
              onSubmit={handleSearch}
            >
              <div className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]">
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  Where
                </div>
                <UnifiedSearchSelect
                  value={destination}
                  onChange={handleUnifiedSearchChange}
                  placeholder="Search destinations or stays"
                  openAbove={isInHero}
                />
              </div>

              <div
                className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]"
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Stop event propagation
                  setIsDatePickerOpen(!isDatePickerOpen);
                }}
              >
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  Check in
                </div>
                {!isMobile && (
                  <div className="text-sm text-[#3566ab] font-medium font-karla text-center w-full">
                    {checkInDate
                      ? checkInDate.toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      : "Add dates"}
                  </div>
                )}
              </div>

              <div
                className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]"
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Stop event propagation
                  setIsDatePickerOpen(!isDatePickerOpen);
                }}
              >
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  Check out
                </div>
                {!isMobile && (
                  <div className="text-sm text-[#3566ab] font-medium font-karla text-center w-full">
                    {checkOutDate
                      ? checkOutDate.toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      : "Add dates"}
                  </div>
                )}
              </div>

              <div
                className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]"
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Stop event propagation
                  setIsGuestSelectorOpen(!isGuestSelectorOpen);
                }}
              >
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  Who
                </div>
                {!isMobile && (
                  <div className="text-sm text-[#3566ab] font-medium font-karla text-center w-full">
                    {guests.adults + guests.children > 0
                      ? `${guests.adults + guests.children} guest${
                          guests.adults + guests.children !== 1 ? "s" : ""
                        }${
                          guests.infants > 0
                            ? `, ${guests.infants} infant${
                                guests.infants !== 1 ? "s" : ""
                              }`
                            : ""
                        }`
                      : "Add guests"}
                  </div>
                )}
              </div>

              <button
                type="submit"
                className="flex items-center justify-center bg-[#3566ab] text-white h-[42px] w-[42px] min-w-[42px] rounded-full border-none mr-2 cursor-pointer transition-all duration-200 shadow-md hover:scale-105 hover:bg-[#2a5089] hover:shadow-lg active:scale-[0.98]"
                style={{ backgroundColor: "#3566ab" }}
              >
                <Search size={20} color="white" />
              </button>

              {/* Date Range Picker Popover */}
              {isDatePickerOpen && (
                <>
                  {/* Mobile backdrop */}
                  {isMobile && (
                    <div
                      className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
                      onClick={() => setIsDatePickerOpen(false)}
                    />
                  )}
                  <div
                    className={`date-picker-popover ${
                      isInHero ? "date-picker-popover-top" : ""
                    }`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <DateRangePicker
                      startDate={checkInDate}
                      endDate={checkOutDate}
                      onDateRangeChange={handleDateRangeChange}
                      onChange={(startDate, endDate) =>
                        handleDateRangeChange(startDate, endDate)
                      }
                      onClose={() => setIsDatePickerOpen(false)}
                    />
                  </div>
                </>
              )}

              {/* Guest Selector Popover */}
              <div className="guest-selector-container">
                {/* Mobile backdrop for guest selector */}
                {isMobile && isGuestSelectorOpen && (
                  <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
                    onClick={() => setIsGuestSelectorOpen(false)}
                  />
                )}
                <GuestSelector
                  adults={guests.adults}
                  children={guests.children}
                  infants={guests.infants}
                  isPetsAllowed={isPetsAllowed}
                  showPetsOption={true}
                  onGuestChange={handleGuestChange}
                  isOpen={isGuestSelectorOpen}
                  onClose={() => setIsGuestSelectorOpen(false)}
                  openAbove={isInHero}
                  isMobileModal={false}
                />
              </div>
            </form>
          )}
        </div>
      )}
    </div>
  );
};

export default ExpandableHeaderSearch;
